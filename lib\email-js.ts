import emailjs from '@emailjs/browser';

// EmailJS Configuration
const EMAILJS_CONFIG = {
  publicKey: 'x_6ueEx3CRUly-ZVu',
  serviceId: 'service_ff9iykb',
  templateIds: {
    free: 'template_free_welcome',
    pro: 'template_pro_welcome',
    enterprise: 'template_pro_welcome',
  },
};

// Initialize EmailJS
let isInitialized = false;

export const initializeEmailJS = () => {
  if (!isInitialized && typeof window !== 'undefined') {
    try {
      emailjs.init({
        publicKey: EMAILJS_CONFIG.publicKey,
      });
      isInitialized = true;
      console.log('✅ EmailJS initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ EmailJS initialization failed:', error);
      return false;
    }
  }
  return isInitialized;
};

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'free' | 'pro' | 'enterprise';
}

export const emailJSService = {
  sendWelcomeEmail: async ({ to, name, planType }: EmailRequest): Promise<boolean> => {
    try {
      console.log(`📧 Sending ${planType} welcome email to ${to}...`);

      // Ensure EmailJS is initialized
      const initialized = initializeEmailJS();

      if (!initialized) {
        console.error('❌ EmailJS not initialized');
        return false;
      }

      // Wait a bit to ensure EmailJS is fully ready
      await new Promise(resolve => setTimeout(resolve, 500));

      const templateId = EMAILJS_CONFIG.templateIds[planType];

      if (!templateId) {
        console.error(`❌ No template found for plan type: ${planType}`);
        return false;
      }

      // Validate email address
      if (!to || !to.includes('@')) {
        console.error('❌ Invalid email address:', to);
        return false;
      }

      // Template parameters that will be sent to EmailJS
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://automlgpt.netlify.app';

      const templateParams = {
        to_email: to, // This is the key field that EmailJS uses
        to_name: name,
        user_name: name,
        plan_type: planType,
        plan_name: planType.charAt(0).toUpperCase() + planType.slice(1),
        from_name: 'AutoML Pro Team',
        reply_to: '<EMAIL>',
        message: `Welcome to AutoML You've successfully signed up for the ${planType} plan.`,
        site_url: baseUrl,
        dashboard_url: `${baseUrl}/dashboard`,
        pricing_url: `${baseUrl}/pricing`,
        chat_url: `${baseUrl}/chat`,
        // Plan-specific parameters
        plan_price: planType === 'pro' ? '$15/month' : planType === 'enterprise' ? 'Contact Sales' : 'Free',
        cta_url: planType === 'free' ? `${baseUrl}/dashboard` : `${baseUrl}/dashboard`,
        cta_text: planType === 'free' ? 'Start Building Models' : planType === 'pro' ? 'Access Pro Dashboard' : 'Contact Sales Team',
      };

      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        templateId,
        templateParams
      );

      if (result.status === 200) {
        console.log(`✅ ${planType} welcome email sent successfully`);
        return true;
      } else {
        console.error('❌ EmailJS failed with status:', result.status, result.text);
        return false;
      }

    } catch (error) {
      console.error('❌ Error sending welcome email via EmailJS:', error);
      return false;
    }
  }
};

// Export configuration for debugging (development only)
export const getEmailJSConfig = () => EMAILJS_CONFIG;

// Export initialization status (development only)
export const getInitializationStatus = () => isInitialized;