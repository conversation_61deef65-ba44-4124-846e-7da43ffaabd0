import emailjs from '@emailjs/browser';

// EmailJS Configuration for Pro Plan Registration Only
const EMAILJS_CONFIG = {
  publicKey: 'x_6ueEx3CRUly-ZVu',
  serviceId: 'service_ff9iykb',
  templateIds: {
    pro: 'template_pro_welcome',
  },
};

// Initialize EmailJS
let isInitialized = false;

export const initializeEmailJS = () => {
  if (!isInitialized && typeof window !== 'undefined') {
    try {
      console.log('🚀 Initializing EmailJS for Pro registration...');
      
      emailjs.init({
        publicKey: EMAILJS_CONFIG.publicKey,
      });
      isInitialized = true;
      console.log('✅ EmailJS initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ EmailJS initialization failed:', error);
      return false;
    }
  }
  return isInitialized;
};

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'pro';
}

export const emailJSService = {
  sendProRegistrationEmail: async ({ to, name }: { to: string; name: string }): Promise<boolean> => {
    try {
      console.log(`📧 Starting Pro registration email to ${to}...`);

      // Ensure EmailJS is initialized
      const initialized = initializeEmailJS();
      
      if (!initialized) {
        console.error('❌ EmailJS not initialized');
        return false;
      }

      // Check if EmailJS is available
      if (typeof emailjs === 'undefined') {
        console.error('❌ EmailJS library not loaded');
        return false;
      }

      // Wait a bit to ensure EmailJS is fully ready
      await new Promise(resolve => setTimeout(resolve, 500));

      const templateId = EMAILJS_CONFIG.templateIds.pro;

      // Validate email address
      if (!to || !to.includes('@')) {
        console.error('❌ Invalid email address:', to);
        return false;
      }

      // Template parameters for Pro registration
      const templateParams = {
        to_email: to,
        to_name: name,
        user_name: name,
        plan_type: 'pro',
        plan_name: 'Professional',
        from_name: 'AutoML Pro Team',
        reply_to: '<EMAIL>',
        message: 'Thank you for registering for our Professional Plan early access!',
        site_url: typeof window !== 'undefined' ? window.location.origin : 'https://automlpro.netlify.app',
        dashboard_url: typeof window !== 'undefined' ? `${window.location.origin}/dashboard` : 'https://automlpro.netlify.app/dashboard',
        pricing_url: typeof window !== 'undefined' ? `${window.location.origin}/pricing` : 'https://automlpro.netlify.app/pricing',
        chat_url: typeof window !== 'undefined' ? `${window.location.origin}/chat` : 'https://automlpro.netlify.app/chat',
      };

      console.log(`📧 Sending Pro registration email...`);
      console.log('📋 Template params:', {
        to_email: templateParams.to_email,
        to_name: templateParams.to_name,
        plan_type: templateParams.plan_type,
        template_id: templateId,
        service_id: EMAILJS_CONFIG.serviceId
      });

      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        templateId,
        templateParams
      );

      console.log('📬 EmailJS result:', result);

      if (result.status === 200) {
        console.log(`✅ Pro registration email sent successfully to ${to}`);
        return true;
      } else {
        console.error('❌ EmailJS failed with status:', result.status, result.text);
        return false;
      }

    } catch (error) {
      console.error('❌ Error sending Pro registration email via EmailJS:', error);
      
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
      
      return false;
    }
  }
};

// Export configuration for debugging
export const getEmailJSConfig = () => EMAILJS_CONFIG;

// Export initialization status
export const getInitializationStatus = () => isInitialized;