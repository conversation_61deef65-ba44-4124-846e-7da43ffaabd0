import { emailJSService } from '@/lib/email-js';

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'pro' | 'enterprise';
}

export const emailService = {
  sendWelcomeEmail: async ({ to, name, planType }: EmailRequest): Promise<boolean> => {
    try {
      console.log(`📧 Sending ${planType} registration email to ${to}...`);
      
      // Only send emails for Pro plan using EmailJS
      if (planType === 'pro') {
        const success = await emailJSService.sendProRegistrationEmail({ to, name });
        
        if (success) {
          console.log(`✅ Pro registration email sent successfully to ${to}`);
          return true;
        } else {
          console.error(`❌ Failed to send Pro registration email to ${to}`);
          return false;
        }
      } else {
        // For Enterprise, no email is sent
        console.log('📧 No email sent for Enterprise registration');
        return false;
      }
    } catch (error) {
      console.error('Email service error:', error);
      return false;
    }
  }
};